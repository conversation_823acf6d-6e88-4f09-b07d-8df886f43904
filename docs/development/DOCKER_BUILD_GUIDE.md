# Docker Commands for OMOP ETL Project

Based on the project structure and Docker setup, here's a comprehensive list of Docker commands for building and testing the OMOP ETL codebase:

## Prerequisites
```bash
# Navigate to project root
cd /Users/<USER>/uclwork/etl/omop-etl
```

## 1. Start Services

### Start Database Services Only
```bash
# Start PostgreSQL and MySQL databases
docker-compose -f scripts/docker-compose.yml up -d omop-cdm-db clinical-db
```

### Start All Services (including development container)
```bash
# Start all services with development profile
docker-compose -f scripts/docker-compose.yml --profile dev up -d
```

### Check Service Status
```bash
# Verify all containers are running
docker-compose -f scripts/docker-compose.yml ps
```

## 2. Clean

### Clean Build Artifacts
```bash
# Remove all build artifacts
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "rm -rf /workspace/build/*"
```

### Clean Docker Volumes (Nuclear Option)
```bash
# Stop all services
docker-compose -f scripts/docker-compose.yml down

# Remove volumes (WARNING: This deletes all database data)
docker-compose -f scripts/docker-compose.yml down -v

# Remove build cache
docker system prune -f
```

### Clean and Rebuild Container
```bash
# Rebuild development container with latest dependencies
docker-compose -f scripts/docker-compose.yml build omop-etl-dev --no-cache
```

## 3. Configure

### CMake Configuration (Debug Build)
```bash
# Configure for debug build with coverage disabled
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --preset docker-debug -DCODE_COVERAGE=OFF"
```

### CMake Configuration (Release Build)
```bash
# Configure for release build
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --preset docker-release"
```

### CMake Configuration with Coverage
```bash
# Configure with code coverage enabled
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --preset docker-debug -DCODE_COVERAGE=ON"
```

### View Configuration Options
```bash
# List available CMake presets
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --list-presets"
```

## 4. Build

### Build All Targets
```bash
# Build entire project
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja"

# Build with reduced parallelism
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja -j2"
```

### Build Specific Libraries
```bash
# Build all libraries
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja omop-etl-lib"

# Build core library only
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja omop_core"

# Build common library only
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja omop_common"

# Build CDM library only
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja omop_cdm"
```

### Build Test Executables Only
```bash
# Build all test executables
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja all_tests"
```

### Build with Verbose Output
```bash
# Build with detailed output
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja -v"
```

## 5. Run Unit Tests

### Run All Unit Tests
```bash
# Execute all unit tests using CTest
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose"
```

### Run Tests by Module

#### Core Module Tests
```bash
# Run all core module tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -R 'core_.*'"

# Run specific core tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/job_manager_test"
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/interfaces_test"
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/record_test"
```

#### Common Module Tests
```bash
# Run all common module tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -R 'common_.*'"

# Run specific common tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/common/configuration_test"
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/common/validation_test"
```

#### CDM Module Tests
```bash
# Run all CDM module tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -R 'cdm_.*'"

# Run specific CDM tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/cdm/table_definitions_test"
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/cdm/omop_tables_test"
```

### Run Tests with XML Output
```bash
# Generate XML test reports
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose --output-junit test_results.xml"
```

### Run Tests in Parallel
```bash
# Run tests in parallel (4 jobs)
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -j4"
```

## 6. Run Integration Tests

### Database Integration Tests
```bash
# Run tests that require database connectivity
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -R 'integration_.*'"
```

### End-to-End Pipeline Tests
```bash
# Run full pipeline integration tests (when available)
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -R 'e2e_.*'"
```

### Load Module Tests (Requires MySQL Libraries)
```bash
# Note: These require container rebuild with MySQL libraries
# Build load module tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja loader_base_test batch_loader_test database_loader_test"

# Run load module tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/load/loader_base_test"
```

## 7. Development Workflow Commands

### Interactive Development Session
```bash
# Start interactive bash session in development container
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash
```

### Watch Mode (Continuous Building)
```bash
# Monitor file changes and rebuild automatically (requires inotify-tools)
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && while inotifywait -r -e modify,create,delete src/; do cmake --build build/docker-debug; done"
```

### Code Coverage Analysis
```bash
# Generate code coverage report
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --preset docker-debug -DCODE_COVERAGE=ON && cd build/docker-debug && ninja && ctest && ninja coverage"
```

## 8. Debugging and Diagnostics

### Check Build Dependencies
```bash
# Verify all dependencies are installed
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "pkg-config --list-all | grep -E '(yaml|json|ssl|odbc)'"
```

### View Build Logs
```bash
# Build with detailed logging
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja -v 2>&1 | tee build.log"
```

### Database Connection Test
```bash
# Test PostgreSQL connection
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "pg_isready -h omop-cdm-db -p 5432 -U omop_user"

# Test MySQL connection
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "mysqladmin ping -h clinical-db -P 3306 -u clinical_user -pclinical_pass"
```

## 9. Cleanup Commands

### Stop All Services
```bash
# Stop all containers
docker-compose -f scripts/docker-compose.yml down
```

### Remove Everything (Nuclear Reset)
```bash
# Stop and remove all containers, networks, and volumes
docker-compose -f scripts/docker-compose.yml down -v --remove-orphans

# Remove all unused Docker resources
docker system prune -a -f
```

## 10. Quick Reference Commands

### Complete Build and Test Cycle
```bash
# One-liner for full build and test
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --preset docker-debug -DCODE_COVERAGE=OFF && cd build/docker-debug && ninja && ctest --output-on-failure --verbose"
```

### Fast Development Cycle
```bash
# Quick rebuild and test specific module
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja omop_core && ./tests/unit/core/job_manager_test"
```

### Memory and Performance Monitoring
```bash
# Monitor container resource usage
docker stats $(docker-compose -f scripts/docker-compose.yml ps -q)
```

These commands provide a complete workflow for developing, building, and testing the OMOP ETL project using Docker. The commands are organized by development phase and include both basic and advanced options for different use cases.
