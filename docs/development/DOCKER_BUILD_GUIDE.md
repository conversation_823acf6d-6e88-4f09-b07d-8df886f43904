# Docker Commands for OMOP ETL Project

Based on the project structure and Docker setup, here's a comprehensive list of Docker commands for building and testing the OMOP ETL codebase:

## Prerequisites
```bash
# Navigate to project root
cd /Users/<USER>/uclwork/etl/omop-etl
```

## 1. Start Services

### Start Database Services Only
```bash
# Start PostgreSQL and MySQL databases
docker-compose -f scripts/docker-compose.yml up -d omop-cdm-db clinical-db
```

### Start All Services (including development container)
```bash
# Start all services with development profile
docker-compose -f scripts/docker-compose.yml --profile dev up -d
```

### Check Service Status
```bash
# Verify all containers are running
docker-compose -f scripts/docker-compose.yml ps
```

## 2. Clean

### Clean Build Artifacts
```bash
# Remove all build artifacts
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "rm -rf /workspace/build/*"
```

### Clean Docker Volumes (Nuclear Option)
```bash
# Stop all services
docker-compose -f scripts/docker-compose.yml down

# Remove volumes (WARNING: This deletes all database data)
docker-compose -f scripts/docker-compose.yml down -v

# Remove build cache
docker system prune -f
```

### Clean and Rebuild Container
```bash
# Rebuild development container with latest dependencies
docker-compose -f scripts/docker-compose.yml build omop-etl-dev --no-cache
```

## 3. Configure

### CMake Configuration (Debug Build)
```bash
# Configure for debug build with coverage disabled
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --preset docker-debug -DCODE_COVERAGE=OFF"
```

### CMake Configuration (Release Build)
```bash
# Configure for release build
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --preset docker-release"
```

### CMake Configuration with Coverage
```bash
# Configure with code coverage enabled
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --preset docker-debug -DCODE_COVERAGE=ON"
```

### View Configuration Options
```bash
# List available CMake presets
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --list-presets"
```

## 4. Build

### Build All Targets
```bash
# Build entire project
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja"

# Build with reduced parallelism
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja -j2"
```

### Build Libraries
```bash
# List all available targets
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja -t targets"

# Build all libraries
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja omop_common omop_core omop_cdm omop_extract omop_transform omop_load omop_service"

# Build core library only
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja omop_core"

# Build common library only
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja omop_common"

# Build CDM library only
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja omop_cdm"
```

### Build Test Executables Only
```bash
# Build all test executables
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja test_common_tests test_core_tests test_cdm_tests test_extract test_transform test_load test_api_tests"

# Build all unit tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja test_unit_tests"

# Build all integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja common_integration_tests core_integration_tests cdm_integration_tests extract_integration_tests transform_integration_tests load_integration_tests service_integration_tests api_integration_tests config_integration_tests security_integration_tests e2e_integration_tests monitoring_integration_tests quality_integration_tests omop_performance_tests"
```

### Build with Verbose Output
```bash
# Build with detailed output
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja -v"
```

## 5. Run Unit Tests

### Run All Unit Tests
```bash
# Execute all unit tests using CTest
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose"
```

### Run Tests by Module

#### Core Module Tests
```bash
# Run all core module tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -R 'core_.*'"

# Run specific core tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/job_manager_test"
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/interfaces_test"
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/record_test"
```

#### Common Module Tests
```bash
# Run all common module tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -R 'common_.*'"

# Run specific common tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/common/configuration_test"
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/common/validation_test"
```

#### CDM Module Tests
```bash
# Run all CDM module tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -R 'cdm_.*'"

# Run specific CDM tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/cdm/table_definitions_test"
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/cdm/omop_tables_test"
```

### Run Tests with XML Output
```bash
# Generate XML test reports
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose --output-junit test_results.xml"
```

### Run Tests in Parallel
```bash
# Run tests in parallel (4 jobs)
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -j4"
```

### Run Specific Unit Test Cases

#### Using Google Test Filters (Recommended)
```bash
# Run specific test case by name
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/test_core_all --gtest_filter='PipelineTest.BasicPipelineCreation'"

# Run all tests in a specific test fixture
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/test_core_all --gtest_filter='PipelineTest.*'"

# Run multiple specific test cases
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/test_core_all --gtest_filter='PipelineTest.BasicPipelineCreation:JobManagerTest.CreateJob'"

# Exclude specific tests (negative filter)
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/test_core_all --gtest_filter='*-PipelineTest.SlowTest'"

# Run tests with wildcard patterns
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/test_core_all --gtest_filter='*Pipeline*'"
```

#### Using CTest to Run Specific Tests
```bash
# Run specific test executable by name
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -R 'pipeline_test'"

# Run tests matching a pattern
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -R 'core.*test'"

# Run tests by label
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -L 'unit'"
```

#### Examples for Different Modules

**Core Module Tests:**
```bash
# Run specific pipeline test
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/pipeline_test --gtest_filter='ETLPipelineTest.StartStopPipeline'"

# Run all job manager tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/job_manager_test --gtest_filter='JobManagerTest.*'"
```

**Common Module Tests:**
```bash
# Run specific configuration test
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/common/configuration_test --gtest_filter='ConfigurationTest.LoadValidConfig'"

# Run all logging tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/common/logging_test --gtest_filter='LoggingTest.*'"
```

**CDM Module Tests:**
```bash
# Run specific table definition test
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/cdm/table_definitions_test --gtest_filter='TableDefinitionsTest.PersonTableStructure'"

# Run all OMOP table tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/cdm/omop_tables_test --gtest_filter='OMOPTablesTest.*'"
```

**Extract Module Tests:**
```bash
# Run specific CSV extractor test
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/extract/csv_extractor_test --gtest_filter='CSVExtractorTest.ExtractValidFile'"

# Run database connector tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/extract/database_connector_test --gtest_filter='DatabaseConnectorTest.*'"
```

**Transform Module Tests:**
```bash
# Run specific transformation test
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/transform/string_transformations_test --gtest_filter='StringTransformationsTest.TrimWhitespace'"

# Run all field transformation tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/transform/field_transformation_test --gtest_filter='FieldTransformationTest.*'"
```

**Load Module Tests:**
```bash
# Run specific batch loader test
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/load/batch_loader_test --gtest_filter='BatchLoaderTest.LoadValidBatch'"

# Run database loader tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/load/database_loader_test --gtest_filter='DatabaseLoaderTest.*'"
```

#### Advanced Test Options

**List Available Tests:**
```bash
# List all test cases in a test executable
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/test_core_all --gtest_list_tests"

# List tests matching a filter
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/test_core_all --gtest_list_tests --gtest_filter='Pipeline*'"
```

**Verbose Test Output:**
```bash
# Run tests with detailed output
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/test_core_all --gtest_filter='PipelineTest.BasicPipelineCreation' --gtest_print_time=1 --gtest_color=yes"

# Run tests with XML output for CI/CD
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/test_core_all --gtest_filter='PipelineTest.*' --gtest_output=xml:test_results.xml"
```

**Repeat Tests for Stability:**
```bash
# Run a test multiple times to check for flakiness
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/test_core_all --gtest_filter='PipelineTest.BasicPipelineCreation' --gtest_repeat=10"

# Run until failure (useful for debugging intermittent failures)
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/core/test_core_all --gtest_filter='PipelineTest.BasicPipelineCreation' --gtest_repeat=-1 --gtest_break_on_failure"
```

**Debugging Failed Tests:**
```bash
# Run with GDB for debugging
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && gdb --args ./tests/unit/core/test_core_all --gtest_filter='PipelineTest.BasicPipelineCreation'"

# Run with Valgrind for memory checking
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && valgrind --tool=memcheck --leak-check=full ./tests/unit/core/test_core_all --gtest_filter='PipelineTest.BasicPipelineCreation'"
```

## 6. Run Integration Tests

### Build and Run All Integration Tests
```bash
# Build and run all integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja common_integration_tests core_integration_tests cdm_integration_tests extract_integration_tests transform_integration_tests load_integration_tests service_integration_tests api_integration_tests config_integration_tests security_integration_tests e2e_integration_tests monitoring_integration_tests quality_integration_tests omop_performance_tests && ctest --output-on-failure --verbose -L integration"
```

### Build and Run Specific Integration Test Modules
```bash
# Common module integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja common_integration_tests && ./common_integration_tests"

# Core module integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja core_integration_tests && ./core_integration_tests"

# CDM module integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja cdm_integration_tests && ./cdm_integration_tests"

# Extract module integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja extract_integration_tests && ./extract_integration_tests"

# Transform module integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja transform_integration_tests && ./transform_integration_tests"

# Load module integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja load_integration_tests && ./load_integration_tests"

# Service integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja service_integration_tests && ./service_integration_tests"

# API integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja api_integration_tests && ./api_integration_tests"

# Configuration integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja config_integration_tests && ./config_integration_tests"

# Security integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja security_integration_tests && ./security_integration_tests"

# End-to-end integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja e2e_integration_tests && ./e2e_integration_tests"

# Monitoring integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja monitoring_integration_tests && ./monitoring_integration_tests"

# Quality integration tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja quality_integration_tests && ./quality_integration_tests"

# Performance tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja omop_performance_tests && ./omop_performance_tests"

# Workflow integration tests (individual executables)
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja test_job_dependencies test_workflow_orchestration test_conditional_workflows test_workflow_recovery"
```

### Run Integration Tests by Category
```bash
# Run all integration tests using CTest
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -L integration"

# Run specific integration test categories
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -L 'integration;core'"

# Run end-to-end tests only
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -L 'integration;e2e'"

# Run performance tests only
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ctest --output-on-failure --verbose -R performance_tests"
```

### Summary of All Available Test Targets

#### Unit Test Targets:
- `test_unit_tests` - All unit tests
- `test_common_tests` - Common module unit tests
- `test_core_tests` - Core module unit tests
- `test_cdm_tests` - CDM module unit tests
- `test_api_tests` - API module unit tests
- `test_extract` - Extract module unit tests
- `test_load` - Load module unit tests
- `test_transform` - Transform module unit tests

#### Integration Test Targets:
- `common_integration_tests` - Common module integration tests
- `core_integration_tests` - Core module integration tests
- `cdm_integration_tests` - CDM module integration tests
- `extract_integration_tests` - Extract module integration tests
- `transform_integration_tests` - Transform module integration tests
- `load_integration_tests` - Load module integration tests
- `service_integration_tests` - Service module integration tests
- `api_integration_tests` - API module integration tests
- `config_integration_tests` - Configuration integration tests
- `security_integration_tests` - Security integration tests
- `e2e_integration_tests` - End-to-end integration tests
- `monitoring_integration_tests` - Monitoring integration tests
- `quality_integration_tests` - Quality integration tests
- `omop_performance_tests` - Performance tests

#### Workflow Test Targets (Individual):
- `test_job_dependencies` - Job dependency tests
- `test_workflow_orchestration` - Workflow orchestration tests
- `test_conditional_workflows` - Conditional workflow tests
- `test_workflow_recovery` - Workflow recovery tests

### Load Module Tests (Requires MySQL Libraries)
```bash
# Note: These require container rebuild with MySQL libraries
# Build load module tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja loader_base_test batch_loader_test database_loader_test"

# Run load module tests
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ./tests/unit/load/loader_base_test"
```

## 7. Development Workflow Commands

### Interactive Development Session
```bash
# Start interactive bash session in development container
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash
```

### Watch Mode (Continuous Building)
```bash
# Monitor file changes and rebuild automatically (requires inotify-tools)
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && while inotifywait -r -e modify,create,delete src/; do cmake --build build/docker-debug; done"
```

### Code Coverage Analysis
```bash
# Generate code coverage report
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --preset docker-debug -DCODE_COVERAGE=ON && cd build/docker-debug && ninja && ctest && ninja coverage"
```

## 8. Debugging and Diagnostics

### Check Build Dependencies
```bash
# Verify all dependencies are installed
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "pkg-config --list-all | grep -E '(yaml|json|ssl|odbc)'"
```

### View Build Logs
```bash
# Build with detailed logging
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja -v 2>&1 | tee build.log"
```

### Database Connection Test
```bash
# Test PostgreSQL connection
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "pg_isready -h omop-cdm-db -p 5432 -U omop_user"

# Test MySQL connection
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "mysqladmin ping -h clinical-db -P 3306 -u clinical_user -pclinical_pass"
```

## 9. Cleanup Commands

### Stop All Services
```bash
# Stop all containers
docker-compose -f scripts/docker-compose.yml down
```

### Remove Everything (Nuclear Reset)
```bash
# Stop and remove all containers, networks, and volumes
docker-compose -f scripts/docker-compose.yml down -v --remove-orphans

# Remove all unused Docker resources
docker system prune -a -f
```

## 10. Quick Reference Commands

### Complete Build and Test Cycle
```bash
# One-liner for full build and test
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace && cmake --preset docker-debug -DCODE_COVERAGE=OFF && cd build/docker-debug && ninja && ctest --output-on-failure --verbose"
```

### Fast Development Cycle
```bash
# Quick rebuild and test specific module
docker-compose -f scripts/docker-compose.yml --profile dev run --rm omop-etl-dev bash -c "cd /workspace/build/docker-debug && ninja omop_core && ./tests/unit/core/job_manager_test"
```

### Memory and Performance Monitoring
```bash
# Monitor container resource usage
docker stats $(docker-compose -f scripts/docker-compose.yml ps -q)
```

These commands provide a complete workflow for developing, building, and testing the OMOP ETL project using Docker. The commands are organized by development phase and include both basic and advanced options for different use cases.
