version: '3.8'

services:
  # OMOP ETL API Service (multi-architecture support)
  omop-etl-api:
    build:
      context: ..
      dockerfile: Dockerfile
      platforms:
        - linux/amd64
        - linux/arm64
    container_name: omop-etl-api
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    depends_on:
      postgres:
        condition: service_healthy
      omop-db:
        condition: service_healthy
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=clinical_db
      - POSTGRES_USER=clinical_user
      - POSTGRES_PASSWORD=clinical_pass
      - OMOP_HOST=omop-db
      - OMOP_PORT=5432
      - OMOP_DB=omop_cdm
      - OMOP_USER=omop_user
      - OMOP_PASSWORD=omop_pass
      - JWT_SECRET=your-secret-key-change-this
      - LOG_LEVEL=INFO
      - OMOP_CONFIG_PATH=/etc/omop-etl
      - OMOP_LOG_PATH=/var/log/omop-etl
      - OMOP_DATA_PATH=/var/lib/omop-etl/data
    ports:
      - "8080:8080"
    volumes:
      - ../config:/etc/omop-etl:ro
      - etl-logs:/var/log/omop-etl
      - etl-data:/var/lib/omop-etl/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - omop-network

  # Source PostgreSQL Database (multi-architecture support)
  postgres:
    image: postgres:15-alpine
    container_name: clinical-db
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    environment:
      - POSTGRES_DB=clinical_db
      - POSTGRES_USER=clinical_user
      - POSTGRES_PASSWORD=clinical_pass
    volumes:
      - clinical-data:/var/lib/postgresql/data
      - ./init-clinical-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U clinical_user"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - omop-network

  # Target OMOP CDM Database (multi-architecture support)
  omop-db:
    image: postgres:15-alpine
    container_name: omop-cdm-db
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    environment:
      - POSTGRES_DB=omop_cdm
      - POSTGRES_USER=omop_user
      - POSTGRES_PASSWORD=omop_pass
    volumes:
      - omop-data:/var/lib/postgresql/data
      - ./init-omop-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U omop_user -d omop_cdm"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - omop-network

  # Development service for building and testing (multi-architecture support)
  omop-etl-dev:
    build:
      context: ..
      dockerfile: Dockerfile.dev
      platforms:
        - linux/amd64
        - linux/arm64
    container_name: omop-etl-dev
    platform: ${DOCKER_PLATFORM:-linux/amd64}
    depends_on:
      postgres:
        condition: service_healthy
      omop-db:
        condition: service_healthy
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=clinical_db
      - POSTGRES_USER=clinical_user
      - POSTGRES_PASSWORD=clinical_pass
      - OMOP_HOST=omop-db
      - OMOP_PORT=5432
      - OMOP_DB=omop_cdm
      - OMOP_USER=omop_user
      - OMOP_PASSWORD=omop_pass
      - LOG_LEVEL=DEBUG
    volumes:
      - ..:/workspace:cached
      - dev_build_cache:/workspace/build
      - dev_conan_cache:/home/<USER>/.conan2
    working_dir: /workspace
    command: tail -f /dev/null  # Keep container running for development
    # Resource limits for build-intensive operations
    deploy:
      resources:
        limits:
          cpus: '6.0'      # Allow up to 6 CPU cores
          memory: 12G      # Allocate 12GB of RAM
        reservations:
          cpus: '2.0'      # Reserve at least 2 CPU cores
          memory: 6G       # Reserve at least 6GB of RAM
    # Additional memory settings for compilation
    shm_size: 4g           # Increase shared memory for compilation
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    networks:
      - omop-network
    profiles:
      - dev

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: omop-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - omop-network

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: omop-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - omop-etl-api
    networks:
      - omop-network

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: omop-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
    networks:
      - omop-network

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: omop-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - omop-network

volumes:
  clinical-data:
    driver: local
  omop-data:
    driver: local
  etl-logs:
    driver: local
  etl-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  dev_build_cache:
    driver: local
  dev_conan_cache:
    driver: local

networks:
  omop-network:
    driver: bridge