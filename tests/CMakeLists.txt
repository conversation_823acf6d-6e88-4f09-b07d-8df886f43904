# tests/unit/CMakeLists.txt - Unit test configuration

# Project declaration for unit tests
project(OMOPUnitTests)

# Include parent directory variables and settings
# All necessary variables are inherited from the parent CMakeLists.txt

# Create aggregate target for all tests
add_custom_target(test_all_tests
    COMMENT "Aggregate target for all unit tests"
)

# Define test variables for consistent configuration
set(OMOP_TEST_COMPILE_FLAGS
    -Wall -Wextra -Wpedantic
    $<$<CONFIG:Debug>:-g -O0>
    $<$<CONFIG:Release>:-O2>
)

set(OMOP_TEST_LINK_LIBRARIES
    gtest
    gtest_main
    gmock
    gmock_main
    omop_core
    omop_common
    spdlog::spdlog
    nlohmann_json::nlohmann_json
    Threads::Threads
    yaml-cpp
    fmt::fmt
    ${PostgreSQL_LIBRARIES}
    ${MySQL_LIBRARIES}
    ${ODBC_LIBRARIES}
    OpenSSL::SSL
    OpenSSL::Crypto
)

# Function to create unit test executables with consistent configuration
function(create_unit_test_executable target_name sources)
    # Create the test executable
    add_executable(${target_name} ${sources})

    # Apply common compile options
    target_compile_options(${target_name} PRIVATE ${OMOP_TEST_COMPILE_FLAGS})

    # Link common test libraries
    target_link_libraries(${target_name} ${OMOP_TEST_LINK_LIBRARIES})

    # Apply static analysis configuration if enabled
    if(CMAKE_CXX_CLANG_TIDY)
        set_target_properties(${target_name} PROPERTIES
            CXX_CLANG_TIDY "${CMAKE_CXX_CLANG_TIDY}"
        )
    endif()

    # Apply coverage configuration if enabled
    if(ENABLE_COVERAGE)
        target_compile_options(${target_name} PRIVATE --coverage)
        target_link_options(${target_name} PRIVATE --coverage)
    endif()

    # Register test with CTest
    add_test(NAME ${target_name} COMMAND ${target_name})

    # Add Valgrind test variant if enabled
    if(ENABLE_VALGRIND AND VALGRIND_EXE)
        add_test(
            NAME ${target_name}_valgrind
            COMMAND ${VALGRIND_COMMAND} $<TARGET_FILE:${target_name}>
        )
        set_tests_properties(${target_name}_valgrind PROPERTIES
            LABELS "valgrind;memory"
        )
    endif()

    # Set test properties
    set_tests_properties(${target_name} PROPERTIES
        LABELS "unit"
        TIMEOUT 300
    )

    # Add to aggregate target dependencies
    add_dependencies(test_all_tests ${target_name})
endfunction()

# Function to add component-specific source files to unit tests
function(add_component_sources target_name component_path source_files)
    target_sources(${target_name} PRIVATE ${source_files})

    # Add component-specific include directories if needed
    target_include_directories(${target_name} PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib/omop/${component_path}
    )
endfunction()

# Add subdirectories containing specific component unit tests
add_subdirectory(unit)

# Create summary target for all unit tests
add_custom_target(test_unit_tests
    DEPENDS test_all_tests
    COMMENT "Running all unit tests"
)

# Create memory checking target for unit tests
if(ENABLE_VALGRIND AND VALGRIND_EXE)
    add_custom_target(test_unit_tests_valgrind
        COMMAND ${CMAKE_CTEST_COMMAND} -L valgrind
        DEPENDS test_all_tests
        COMMENT "Running unit tests with Valgrind memory checking"
    )
endif()

# Create coverage target for unit tests
if(ENABLE_COVERAGE)
    find_program(LCOV_EXECUTABLE lcov)
    find_program(GENHTML_EXECUTABLE genhtml)

    if(LCOV_EXECUTABLE AND GENHTML_EXECUTABLE)
        # Create coverage directory
        file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/coverage)

        # Add coverage target
        add_custom_target(unit_test_coverage
            # Reset coverage counters
            COMMAND ${LCOV_EXECUTABLE} --zerocounters --directory .

            # Run unit tests
            COMMAND ${CMAKE_CTEST_COMMAND} -L unit

            # Capture coverage data
            COMMAND ${LCOV_EXECUTABLE} --capture --directory . --output-file coverage/unit_tests.info

            # Remove external dependencies from coverage
            COMMAND ${LCOV_EXECUTABLE} --remove coverage/unit_tests.info '/usr/*' '*/tests/*' '*/gtest/*' --output-file coverage/unit_tests_filtered.info

            # Generate HTML report
            COMMAND ${GENHTML_EXECUTABLE} coverage/unit_tests_filtered.info --output-directory coverage/html

            WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
            DEPENDS test_all_tests
            COMMENT "Generating unit test coverage report"
        )

        # Add to aggregate coverage target
        add_dependencies(test_all_tests_with_coverage unit_test_coverage)
    else()
        message(WARNING "lcov or genhtml not found, coverage report generation disabled")
    endif()
endif()

# Installation configuration for unit test executables
install(DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit
    DESTINATION bin/tests
    USE_SOURCE_PERMISSIONS
    FILES_MATCHING PATTERN "*test*"
)

# Unit test configuration summary
message(STATUS "Unit Test Configuration:")
message(STATUS "  Unit test framework: Google Test ${GTEST_VERSION}")
message(STATUS "  Mock framework: Google Mock")
message(STATUS "  Coverage reporting: ${ENABLE_COVERAGE}")
message(STATUS "  Memory checking: ${ENABLE_VALGRIND}")
message(STATUS "  Timeout per test: 300 seconds")
