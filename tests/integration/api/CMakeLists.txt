# API integration tests
set(API_INTEGRATION_TEST_SOURCES
    test_api_integration.cpp
    test_grpc_api_integration.cpp
    test_rest_api_integration.cpp
)

add_executable(api_integration_tests ${API_INTEGRATION_TEST_SOURCES})

target_link_libraries(api_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        omop_service
        integration_test_helpers
        GTest::GTest
        GTest::GTestMain
        GTest::GMock
)

target_include_directories(api_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/app/api
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME api_integration_tests
    COMMAND api_integration_tests
)

set_tests_properties(api_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;api"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
) 