# Configuration integration tests
set(CONFIG_INTEGRATION_TEST_SOURCES
    test_configuration_management.cpp
)

add_executable(config_integration_tests ${CONFIG_INTEGRATION_TEST_SOURCES})

target_link_libraries(config_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        omop_service
        integration_test_helpers
        GTest::GTest
        GTest::GTestMain
        GTest::GMock
)

target_include_directories(config_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME config_integration_tests
    COMMAND config_integration_tests
)

set_tests_properties(config_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;config"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
) 