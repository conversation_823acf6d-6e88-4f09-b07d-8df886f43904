# End-to-end integration tests
set(E2E_INTEGRATION_TEST_SOURCES
    test_etl_service_integration.cpp
    test_full_pipeline_integration.cpp
    test_multi_tenant_etl_e2e.cpp
)

add_executable(e2e_integration_tests ${E2E_INTEGRATION_TEST_SOURCES})

target_link_libraries(e2e_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        omop_service
        integration_test_helpers
        GTest::GTest
        GTest::GTestMain
        GTest::GMock
)

target_include_directories(e2e_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME e2e_integration_tests
    COMMAND e2e_integration_tests
)

set_tests_properties(e2e_integration_tests PROPERTIES
    TIMEOUT 600
    LABELS "integration;e2e"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
) 