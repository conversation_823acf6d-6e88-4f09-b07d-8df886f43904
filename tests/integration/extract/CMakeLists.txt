# Extract integration tests
set(EXTRACT_INTEGRATION_TEST_SOURCES
    test_csv_extractor_integration.cpp
    test_database_extractor_integration.cpp
    test_json_extractor_integration.cpp
    test_multi_source_extraction_integration.cpp
)

add_executable(extract_integration_tests ${EXTRACT_INTEGRATION_TEST_SOURCES})

target_link_libraries(extract_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        omop_service
        integration_test_helpers
        GTest::GTest
        GTest::GTestMain
        GTest::GMock
)

target_include_directories(extract_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME extract_integration_tests
    COMMAND extract_integration_tests
)

set_tests_properties(extract_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;extract"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
) 