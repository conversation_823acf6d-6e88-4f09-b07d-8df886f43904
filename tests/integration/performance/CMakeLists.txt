// tests/performance/CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# Performance test configuration
set(PERFORMANCE_TEST_SOURCES
    test_load_performance.cpp
    test_memory_usage.cpp
    test_concurrent_operations.cpp
    test_scalability.cpp
)

# Create performance test executable
add_executable(omop_performance_tests ${PERFORMANCE_TEST_SOURCES})

target_link_libraries(omop_performance_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
        omop_service
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        Threads::Threads
)

# Add performance test
add_test(NAME performance_tests COMMAND omop_performance_tests)