# CMakeLists.txt for Workflow Integration Tests

set(WORKFLOW_INTEGRATION_TEST_SOURCES
    test_job_dependencies.cpp
    test_workflow_orchestration.cpp
    test_conditional_workflows.cpp
    test_workflow_recovery.cpp
)

foreach(test_source ${WORKFLOW_INTEGRATION_TEST_SOURCES})
    get_filename_component(test_name ${test_source} NAME_WE)
    add_executable(${test_name} ${test_source})
    
    target_link_libraries(${test_name}
        PRIVATE
            omop_common
            omop_core
            omop_cdm
            omop_extract
            omop_transform
            omop_load
            omop_service
            integration_test_helpers
            GTest::GTest
            GTest::GTestMain
            GTest::GMock
            Threads::Threads
    )
    
    target_include_directories(${test_name}
        PRIVATE
            ${CMAKE_SOURCE_DIR}/src/lib
            ${CMAKE_SOURCE_DIR}/tests/integration
    )
    
    # Add test to CTest
    add_test(
        NAME integration.workflow.${test_name}
        COMMAND ${test_name}
        WORKING_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
    )
    
    # Set test properties
    set_tests_properties(integration.workflow.${test_name}
        PROPERTIES
            LABELS "integration;workflow"
            TIMEOUT 300
            ENVIRONMENT "OMOP_TEST_DB_HOST=localhost;OMOP_TEST_DB_PORT=5432"
    )
endforeach()

# Copy test data files if any
file(GLOB WORKFLOW_TEST_DATA_FILES "${CMAKE_CURRENT_SOURCE_DIR}/test_data/*")
if(WORKFLOW_TEST_DATA_FILES)
    file(COPY ${WORKFLOW_TEST_DATA_FILES}
         DESTINATION ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/test_data/workflow)
endif()