# Unit tests for OMOP CDM library

# Test source files in the cdm directory
set(CDM_TEST_SOURCES
    table_definitions_test.cpp
    omop_tables_test.cpp
)

# Create individual test executables for each test file
foreach(test_source ${CDM_TEST_SOURCES})
    # Extract test name from filename (remove .cpp extension)
    get_filename_component(test_name ${test_source} NAME_WE)

    # Create the test executable using the parent function
    create_unit_test_executable(${test_name} ${test_source})

    # Link CDM library
    target_link_libraries(${test_name} omop_cdm)

    # Add component-specific include directories
    target_include_directories(${test_name} PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/lib/cdm
        ${CMAKE_SOURCE_DIR}/src/lib/common
        ${CMAKE_CURRENT_SOURCE_DIR}
    )

    # Set test-specific properties
    set_target_properties(${test_name} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit/cdm
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )

    # Add coverage flags if enabled
    if(ENABLE_COVERAGE)
        target_compile_options(${test_name} PRIVATE --coverage)
        target_link_options(${test_name} PRIVATE --coverage)
    endif()
endforeach()

# Create a combined test executable for all CDM tests
create_unit_test_executable(test_cdm_all "${CDM_TEST_SOURCES}")

# Link CDM library for combined test
target_link_libraries(test_cdm_all omop_cdm)

# Add component-specific include directories for combined test
target_include_directories(test_cdm_all PRIVATE
    ${CMAKE_SOURCE_DIR}/src/lib
    ${CMAKE_SOURCE_DIR}/src/lib/cdm
    ${CMAKE_SOURCE_DIR}/src/lib/common
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Set properties for combined test
set_target_properties(test_cdm_all PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit/cdm
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
)

# Add coverage flags if enabled for combined test
if(ENABLE_COVERAGE)
    target_compile_options(test_cdm_all PRIVATE --coverage)
    target_link_options(test_cdm_all PRIVATE --coverage)
endif()

# Create convenience target to run all CDM tests
add_custom_target(test_cdm_tests
    COMMAND ${CMAKE_CTEST_COMMAND} -L "unit" --output-on-failure
    DEPENDS test_cdm_all
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running all CDM unit tests"
)

# Copy test data files if any exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test_data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/test_data
        DESTINATION ${CMAKE_CURRENT_BINARY_DIR}
    )
endif()

# Test configuration summary
message(STATUS "CDM Unit Tests Configuration:")
message(STATUS "  Test files: ${CDM_TEST_SOURCES}")
message(STATUS "  Output directory: ${CMAKE_BINARY_DIR}/tests/unit/cdm")
message(STATUS "  C++ Standard: 20")
